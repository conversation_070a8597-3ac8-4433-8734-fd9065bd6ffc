ssh -i "tiktokshop-key.pem" ubuntu@44.204.202.129
cd /opt/tiktokshop
git pull

# Kiểm tra trạng thái
pm2 status
pm2 logs tiktokshop-backend

# Dừng PM2 nếu đang chạy
pm2 delete all

# Load environment variables from .env.production
export $(cat .env.production | grep -v '^#' | xargs)

# Cài đặt dependencies
npm install

# Build ứng dụng
npm run build

# Run migration with production environment
npm run migration:run

# Khởi động với PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup